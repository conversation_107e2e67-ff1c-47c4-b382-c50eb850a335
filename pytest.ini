[pytest]
markers =
    integration_docker: Tests that require a full Docker environment (end-to-end)
    integration: Backend integration tests
    unit: Unit tests
    slow: Slow tests
    asyncio: Tests that use asyncio
    docker_workflow: Tests that verify complete workflows in Docker environment
    docker_communication: Tests that verify inter-service communication

# Docker settings for pytest-docker
docker_compose_project_name = modportertest
# The following line can be useful, though our conftest.py also specifies the file.
# It helps if someone runs pytest with pytest-docker manually without class-scoped fixtures.
docker_compose = tests/docker-compose.test.yml
testpaths =
    tests
    backend/tests
    ai-engine/tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
