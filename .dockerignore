# Git
.git
.gitignore
README.md

# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
.venv
venv/
env/

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Build outputs
dist/
build/
*.egg-info/

# Logs
logs
*.log

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Coverage
coverage/
.nyc_output

# Docker
Dockerfile*
docker-compose*
.dockerignore