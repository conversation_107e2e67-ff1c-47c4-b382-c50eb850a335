# Privacy Notice: ModPorter AI Training Data

## 1. Introduction

This document explains how ModPorter AI collects and uses data when you use our mod conversion service and provide feedback. Our goal is to be transparent about our practices and how we use your data to improve our AI conversion model. Your use of the feedback feature helps us enhance the service for all users.

## 2. What Data We Collect

To improve our AI model, we collect and store the following types of data associated with your conversion jobs:

*   **User Input Files**: The original mod files (e.g., `.jar`, `.zip`) that you upload for conversion are stored on our servers.
*   **Generated Output Files**: The converted files (e.g., `.mcaddon`, `.zip`) generated by our AI process are stored.
*   **User Feedback**:
    *   **Explicit Feedback**: We collect the feedback you explicitly provide, such as "thumbs up" or "thumbs down" ratings for a conversion.
    *   **Textual Comments**: Any textual comments or descriptions you submit alongside your feedback are also stored.
    *   **Implicit Feedback**: Currently, we do not actively collect implicit feedback (e.g., tracking downloads or re-attempts as direct feedback signals for specific jobs), but this may be considered in the future to further enhance model training.
*   **Job Identifiers**: We use system-generated unique identifiers (Job IDs) to link your input files, output files, and any feedback you provide for a specific conversion task.
*   **User Identifiers (Optional)**: If you provide a user identifier (e.g., username, email) when submitting feedback, this may be stored to help us understand feedback patterns or communicate with you if necessary. Providing this is optional.

## 3. How We Use Your Data

The data we collect is primarily used for the following purpose:

*   **To improve the accuracy, efficiency, and overall quality of the AI conversion model.**

Your input files, the corresponding output files generated by the AI, and your explicit feedback (ratings and comments) serve as a valuable training dataset. This data helps our AI system learn from real-world use cases and user experiences. By analyzing successful conversions (e.g., those rated "thumbs up") and problematic ones (e.g., rated "thumbs down" with comments explaining issues), we can refine our algorithms. This process is similar to Reinforcement Learning from Human Feedback (RLHF), where the AI learns to make better predictions and generate higher-quality conversions in the future.

## 4. Data Storage and Security

*   **Storage**: All collected data, including uploaded files, generated files, and feedback, is stored on secure servers.
*   **Security Measures**: We implement reasonable administrative, technical, and physical security measures designed to protect your data from unauthorized access, use, alteration, or disclosure.
*   **Access Control**: Access to this data is restricted to authorized personnel involved in the development, maintenance, and improvement of the ModPorter AI service.

## 5. Data Retention

Data (input files, output files, feedback, and job identifiers) is retained as long as it is deemed necessary for the primary purpose of AI model improvement and analysis. We will periodically review our data retention needs and may delete or anonymize older data when it is no longer providing significant value for model training or operational requirements.

## 6. Your Choices / Opt-Out

*   **Feedback Submission**: Providing feedback (both ratings and comments) is entirely optional. You can use the conversion service without submitting feedback.
*   **Data Storage for Conversions**: When you upload a file for conversion, the input and output files are stored as part of the system's operation and for potential model improvement as described. Currently, the system does not offer an automated way to opt-out of this storage for submitted jobs.
*   **Data Deletion Requests**: While an automated system for data deletion is not currently in place, you may contact us with concerns or specific requests regarding your data (see Contact Us section). We will address such requests on a case-by-case basis, subject to our operational needs for model integrity and improvement.

## 7. Updates to This Policy

We may update this privacy notice from time to time to reflect changes in our practices or for other operational, legal, or regulatory reasons. We will notify you of any significant changes by posting the new notice on our platform or through other appropriate communication channels.

## 8. Contact Us

If you have any questions or concerns about this privacy notice or our data handling practices, please contact us:

*   **Email**: [<EMAIL>](mailto:<EMAIL>) (Please replace with a real contact method if available)
*   **Issue Tracker**: You can also raise an issue on our project's GitHub issue tracker (if applicable, provide link).

We value your trust and are committed to handling your data responsibly.
---
*Last Updated: [Current Date - to be filled in upon merge/release]*
