{"$schema": "https://opencode.ai/config.json", "provider": {"openrouter": {"npm": "@openrouter/ai-sdk-provider", "name": "OpenRouter", "options": {"apiKey": "YOUR_OPENROUTER_API_KEY_HERE", "baseURL": "https://openrouter.ai/api/v1"}, "models": {"openrouter/cypher-alpha:free": {"name": "<PERSON><PERSON> (Free)"}, "mistralai/mistral-small-3.2-24b-instruct:free": {"name": "Mistral Small 3.2 24B (Free)"}, "moonshotai/kimi-dev-72b:free": {"name": "<PERSON><PERSON> 72B (Free)"}, "deepseek/deepseek-r1-0528-qwen3-8b:free": {"name": "DeepSeek R1 Qwen3 8B (Free)"}, "deepseek/deepseek-r1-0528:free": {"name": "DeepSeek R1 (Free)"}, "mistralai/devstral-small:free": {"name": "<PERSON><PERSON><PERSON> Small (Free)"}}}}, "model": "openrouter/openrouter/cypher-alpha:free"}