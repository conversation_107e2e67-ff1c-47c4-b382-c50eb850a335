# HTTP Client
httpx>=0.25.0

# Core Framework
fastapi==0.116.0
uvicorn[standard]==0.35.0
pydantic==2.11.7
pydantic-settings==2.10.1
python-dateutil==2.9.0.post0

# Database
sqlalchemy>=2.0.23
asyncpg>=0.29
psycopg2-binary>=2.9.7
alembic==1.16.2
pgvector>=0.1.1
aiosqlite>=0.19.0  # For SQLite async support in tests

# Caching
redis[asyncio]==6.2.0

# File handling
python-multipart==0.0.20
tomli==2.2.1  # TOML parsing for Python <3.11
python-magic==0.4.27

# HTTP Client for file downloads
httpx==0.28.1

# Testing
pytest>=8.2
pytest-asyncio==1.0.0
pytest-cov==6.2.1
pytest-timeout==2.3.1

# Code Quality
ruff==0.12.2
black==25.1.0

# Utilities
python-dotenv==1.1.1