name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  create-release:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Create Release
      uses: softprops/action-gh-release@v2
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        name: Release ${{ github.ref_name }}
        body: |
          ## What's Changed
          
          ### Features
          - New conversion capabilities
          - Enhanced smart assumptions
          - Improved user interface
          
          ### Bug Fixes
          - Various stability improvements
          - Better error handling
          
          ### Documentation
          - Updated API documentation
          - Enhanced user guides
          
          **Full Changelog**: https://github.com/${{ github.repository }}/compare/v1.0.0...${{ github.ref_name }}
        draft: false
        prerelease: false