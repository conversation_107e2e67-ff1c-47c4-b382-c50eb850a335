INFO:     Will watch for changes in these directories: ['/home/<USER>/ModPorter-AI/ai-engine']
INFO:     Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
INFO:     Started reloader process [97038] using WatchFiles
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [97093]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
ERROR:src.main:Failed to initialize AI Engine: Error 111 connecting to localhost:6379. Connection refused.
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/connection.py", line 302, in connect_check_health
    await self.retry.call_with_retry(
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/retry.py", line 71, in call_with_retry
    return await do()
           ^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/connection.py", line 755, in _connect
    reader, writer = await asyncio.open_connection(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/asyncio/streams.py", line 48, in open_connection
    transport, _ = await loop.create_connection(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 2043, in create_connection
  File "uvloop/loop.pyx", line 2020, in uvloop.loop.Loop.create_connection
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 177, in startup_event
    await redis_client.ping()
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/client.py", line 672, in execute_command
    conn = self.connection or await pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/connection.py", line 1141, in get_connection
    await self.ensure_connection(connection)
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/connection.py", line 1174, in ensure_connection
    await connection.connect()
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/connection.py", line 296, in connect
    await self.connect_check_health(check_health=True)
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/connection.py", line 310, in connect_check_health
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to localhost:6379. Connection refused.
ERROR:    Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/connection.py", line 302, in connect_check_health
    await self.retry.call_with_retry(
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/retry.py", line 71, in call_with_retry
    return await do()
           ^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/connection.py", line 755, in _connect
    reader, writer = await asyncio.open_connection(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/asyncio/streams.py", line 48, in open_connection
    transport, _ = await loop.create_connection(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 2043, in create_connection
  File "uvloop/loop.pyx", line 2020, in uvloop.loop.Loop.create_connection
ConnectionRefusedError: [Errno 111] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 177, in startup_event
    await redis_client.ping()
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/client.py", line 672, in execute_command
    conn = self.connection or await pool.get_connection()
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/connection.py", line 1141, in get_connection
    await self.ensure_connection(connection)
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/connection.py", line 1174, in ensure_connection
    await connection.connect()
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/connection.py", line 296, in connect
    await self.connect_check_health(check_health=True)
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/redis/asyncio/connection.py", line 310, in connect_check_health
    raise ConnectionError(self._error_message(e))
redis.exceptions.ConnectionError: Error 111 connecting to localhost:6379. Connection refused.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/starlette/routing.py", line 692, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/starlette/routing.py", line 569, in __aenter__
    await self._router.startup()
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/starlette/routing.py", line 669, in startup
    await handler()
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 196, in startup_event
    raise HTTPException(status_code=503, detail="Service initialization failed")
fastapi.exceptions.HTTPException: 503: Service initialization failed

ERROR:    Application startup failed. Exiting.
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
Process SpawnProcess-2:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 20, in <module>
    from src.crew.conversion_crew import ModPorterConversionCrew
  File "/home/<USER>/ModPorter-AI/ai-engine/src/crew/conversion_crew.py", line 46
    clean_ollama_model = ollama_model.replace("ollama/", "")
    ^^^^^^^^^^^^^^^^^^
SyntaxError: expected 'except' or 'finally' block
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [28502]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [28502]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [30201]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
INFO:     127.0.0.1:36326 - "GET /api/v1/health HTTP/1.1" 200 OK
INFO:src.main:Started conversion job aeb23b35-0a1c-422a-8f5e-d1f503a21585
INFO:     127.0.0.1:36338 - "POST /api/v1/convert HTTP/1.1" 200 OK
INFO:src.main:Processing conversion for job aeb23b35-0a1c-422a-8f5e-d1f503a21585
ERROR:src.main:Conversion failed for job aeb23b35-0a1c-422a-8f5e-d1f503a21585: [Errno 13] Permission denied: '/shared_data'
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 308, in process_conversion
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
  File "<frozen os>", line 215, in makedirs
  File "<frozen os>", line 225, in makedirs
PermissionError: [Errno 13] Permission denied: '/shared_data'
INFO:     127.0.0.1:34572 - "GET /api/v1/status/aeb23b35-0a1c-422a-8f5e-d1f503a21585 HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [30201]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [31743]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
INFO:src.main:Started conversion job 248e4258-f0dd-41b0-87b0-760f4b161db0
INFO:     127.0.0.1:55644 - "POST /api/v1/convert HTTP/1.1" 200 OK
INFO:src.main:Processing conversion for job 248e4258-f0dd-41b0-87b0-760f4b161db0
ERROR:src.main:Conversion failed for job 248e4258-f0dd-41b0-87b0-760f4b161db0: [Errno 13] Permission denied: '/app'
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 308, in process_conversion
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
  File "<frozen os>", line 215, in makedirs
  File "<frozen os>", line 225, in makedirs
PermissionError: [Errno 13] Permission denied: '/app'
INFO:     127.0.0.1:58070 - "GET /api/v1/status/248e4258-f0dd-41b0-87b0-760f4b161db0 HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [31743]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [32674]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
INFO:src.main:Started conversion job f5e664ab-a3f8-499c-98da-3e6bbc9a24ac
INFO:     127.0.0.1:58066 - "POST /api/v1/convert HTTP/1.1" 200 OK
INFO:src.main:Processing conversion for job f5e664ab-a3f8-499c-98da-3e6bbc9a24ac
ERROR:src.main:Conversion failed for job f5e664ab-a3f8-499c-98da-3e6bbc9a24ac: name 'request' is not defined
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 304, in process_conversion
    output_path = os.path.join("/app/conversion_outputs", f"{request.job_id}_converted.mcaddon")
                                                             ^^^^^^^
NameError: name 'request' is not defined
INFO:     127.0.0.1:60082 - "GET /api/v1/status/f5e664ab-a3f8-499c-98da-3e6bbc9a24ac HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [32674]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [33500]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
INFO:src.main:Started conversion job c0d72ade-8707-4ece-b4a3-dc8c5398577d
INFO:     127.0.0.1:45330 - "POST /api/v1/convert HTTP/1.1" 200 OK
INFO:src.main:Processing conversion for job c0d72ade-8707-4ece-b4a3-dc8c5398577d
ERROR:src.main:Conversion failed for job c0d72ade-8707-4ece-b4a3-dc8c5398577d: [Errno 13] Permission denied: '/app'
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 307, in process_conversion
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
  File "<frozen os>", line 215, in makedirs
  File "<frozen os>", line 225, in makedirs
PermissionError: [Errno 13] Permission denied: '/app'
INFO:     127.0.0.1:57322 - "GET /api/v1/status/c0d72ade-8707-4ece-b4a3-dc8c5398577d HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [33500]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [34672]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [34672]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [34925]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [34925]
Process SpawnProcess-10:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 20, in <module>
    from src.crew.conversion_crew import ModPorterConversionCrew
  File "/home/<USER>/ModPorter-AI/ai-engine/src/crew/conversion_crew.py", line 467
    except Exception as e:
    ^^^^^^
SyntaxError: invalid syntax
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
Process SpawnProcess-11:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 20, in <module>
    from src.crew.conversion_crew import ModPorterConversionCrew
  File "/home/<USER>/ModPorter-AI/ai-engine/src/crew/conversion_crew.py", line 467
    finally:
    ^^^^^^^
SyntaxError: invalid syntax
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [74254]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
INFO:src.main:Started conversion job 94eed48c-4f8d-4ffb-9c8b-9aad02ba328b
INFO:src.main:Processing conversion for job 94eed48c-4f8d-4ffb-9c8b-9aad02ba328b
ERROR:src.main:Conversion failed for job 94eed48c-4f8d-4ffb-9c8b-9aad02ba328b: [Errno 13] Permission denied: '/app'
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 307, in process_conversion
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
  File "<frozen os>", line 215, in makedirs
  File "<frozen os>", line 225, in makedirs
PermissionError: [Errno 13] Permission denied: '/app'
INFO:src.main:Started conversion job 289a89d0-796e-4c7e-b605-4c9bc5c52d39
INFO:     127.0.0.1:55864 - "POST /api/v1/convert HTTP/1.1" 200 OK
INFO:src.main:Processing conversion for job 289a89d0-796e-4c7e-b605-4c9bc5c52d39
ERROR:src.main:Conversion failed for job 289a89d0-796e-4c7e-b605-4c9bc5c52d39: [Errno 13] Permission denied: '/app'
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 307, in process_conversion
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
  File "<frozen os>", line 215, in makedirs
  File "<frozen os>", line 225, in makedirs
PermissionError: [Errno 13] Permission denied: '/app'
INFO:     127.0.0.1:51890 - "GET /api/v1/status/289a89d0-796e-4c7e-b605-4c9bc5c52d39 HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [74254]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [75474]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [75474]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [75894]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [75894]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [76550]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
INFO:src.main:Started conversion job 46c39956-d35e-4e69-aa40-da259f627bd9
INFO:     127.0.0.1:53624 - "POST /api/v1/convert HTTP/1.1" 200 OK
INFO:src.main:Processing conversion for job 46c39956-d35e-4e69-aa40-da259f627bd9
ERROR:src.main:Conversion failed for job 46c39956-d35e-4e69-aa40-da259f627bd9: [Errno 13] Permission denied: '/app'
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 307, in process_conversion
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
  File "<frozen os>", line 215, in makedirs
  File "<frozen os>", line 225, in makedirs
PermissionError: [Errno 13] Permission denied: '/app'
INFO:     127.0.0.1:53638 - "GET /api/v1/status/46c39956-d35e-4e69-aa40-da259f627bd9 HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [76550]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [77629]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
INFO:src.main:Started conversion job a203ef51-f3e8-4f5f-9a74-a364f8175c0b
INFO:     127.0.0.1:59866 - "POST /api/v1/convert HTTP/1.1" 200 OK
INFO:src.main:Processing conversion for job a203ef51-f3e8-4f5f-9a74-a364f8175c0b
ERROR:src.main:Conversion failed for job a203ef51-f3e8-4f5f-9a74-a364f8175c0b: [Errno 13] Permission denied: '/app'
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 307, in process_conversion
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
  File "<frozen os>", line 215, in makedirs
  File "<frozen os>", line 225, in makedirs
PermissionError: [Errno 13] Permission denied: '/app'
INFO:     127.0.0.1:59884 - "GET /api/v1/status/a203ef51-f3e8-4f5f-9a74-a364f8175c0b HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [77629]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [82822]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [82822]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [83130]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
INFO:src.main:Started conversion job f5233b0a-bd47-4c0d-87c3-87a037ad5cbf
INFO:     127.0.0.1:36868 - "POST /api/v1/convert HTTP/1.1" 200 OK
INFO:src.main:Processing conversion for job f5233b0a-bd47-4c0d-87c3-87a037ad5cbf
ERROR:src.main:Conversion failed for job f5233b0a-bd47-4c0d-87c3-87a037ad5cbf: [Errno 13] Permission denied: '/app'
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 307, in process_conversion
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
  File "<frozen os>", line 215, in makedirs
  File "<frozen os>", line 225, in makedirs
PermissionError: [Errno 13] Permission denied: '/app'
INFO:     127.0.0.1:36890 - "GET /api/v1/status/f5233b0a-bd47-4c0d-87c3-87a037ad5cbf HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [83130]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [88006]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [88006]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [89229]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [89229]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [89408]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
INFO:src.main:Started conversion job 9a5e0907-924d-4745-9126-6ee9b94932e2
INFO:     127.0.0.1:35784 - "POST /api/v1/convert HTTP/1.1" 200 OK
INFO:src.main:Processing conversion for job 9a5e0907-924d-4745-9126-6ee9b94932e2
ERROR:src.main:Conversion failed for job 9a5e0907-924d-4745-9126-6ee9b94932e2: [Errno 13] Permission denied: '/app'
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 319, in process_conversion
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
  File "<frozen os>", line 215, in makedirs
  File "<frozen os>", line 225, in makedirs
PermissionError: [Errno 13] Permission denied: '/app'
INFO:     127.0.0.1:42568 - "GET /api/v1/status/9a5e0907-924d-4745-9126-6ee9b94932e2 HTTP/1.1" 200 OK
INFO:src.main:Started conversion job e24067f2-1a09-41f4-980e-abbc725fbe16
INFO:     127.0.0.1:50916 - "POST /api/v1/convert HTTP/1.1" 200 OK
INFO:src.main:Processing conversion for job e24067f2-1a09-41f4-980e-abbc725fbe16
ERROR:src.main:Conversion failed for job e24067f2-1a09-41f4-980e-abbc725fbe16: [Errno 13] Permission denied: '/app'
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 319, in process_conversion
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
  File "<frozen os>", line 215, in makedirs
  File "<frozen os>", line 225, in makedirs
PermissionError: [Errno 13] Permission denied: '/app'
INFO:     127.0.0.1:53122 - "GET /api/v1/status/e24067f2-1a09-41f4-980e-abbc725fbe16 HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/agents/java_analyzer.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [89408]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [92965]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
INFO:src.main:Started conversion job d951c07b-c2a1-41ba-b8b5-53e55a04c833
INFO:     127.0.0.1:49480 - "POST /api/v1/convert HTTP/1.1" 200 OK
INFO:src.main:Processing conversion for job d951c07b-c2a1-41ba-b8b5-53e55a04c833
ERROR:src.main:Conversion failed for job d951c07b-c2a1-41ba-b8b5-53e55a04c833: [Errno 13] Permission denied: '/app'
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 319, in process_conversion
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
  File "<frozen os>", line 215, in makedirs
  File "<frozen os>", line 225, in makedirs
PermissionError: [Errno 13] Permission denied: '/app'
INFO:     127.0.0.1:59194 - "GET /api/v1/status/d951c07b-c2a1-41ba-b8b5-53e55a04c833 HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/agents/java_analyzer.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [92965]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [1491]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
INFO:src.main:Started conversion job 1661edc1-80a7-4734-92e3-aa9fb2df3140
INFO:     127.0.0.1:45418 - "POST /api/v1/convert HTTP/1.1" 200 OK
INFO:src.main:Processing conversion for job 1661edc1-80a7-4734-92e3-aa9fb2df3140
ERROR:src.main:Conversion failed for job 1661edc1-80a7-4734-92e3-aa9fb2df3140: [Errno 13] Permission denied: '/app'
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 319, in process_conversion
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
  File "<frozen os>", line 215, in makedirs
  File "<frozen os>", line 225, in makedirs
PermissionError: [Errno 13] Permission denied: '/app'
INFO:     127.0.0.1:37500 - "GET /api/v1/status/1661edc1-80a7-4734-92e3-aa9fb2df3140 HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/agents/java_analyzer.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [1491]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [3783]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
INFO:src.main:Started conversion job 20304b32-4bf6-48e8-ace6-f8a9a32b2a24
INFO:     127.0.0.1:40660 - "POST /api/v1/convert HTTP/1.1" 200 OK
INFO:src.main:Processing conversion for job 20304b32-4bf6-48e8-ace6-f8a9a32b2a24
ERROR:src.main:Conversion failed for job 20304b32-4bf6-48e8-ace6-f8a9a32b2a24: [Errno 13] Permission denied: '/app'
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 319, in process_conversion
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
  File "<frozen os>", line 215, in makedirs
  File "<frozen os>", line 225, in makedirs
PermissionError: [Errno 13] Permission denied: '/app'
INFO:     127.0.0.1:51736 - "GET /api/v1/status/20304b32-4bf6-48e8-ace6-f8a9a32b2a24 HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [3783]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [5746]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
INFO:src.main:Started conversion job 7d3c1114-b9f8-42d7-9ba3-a369d347a1f3
INFO:     127.0.0.1:32926 - "POST /api/v1/convert HTTP/1.1" 200 OK
INFO:src.main:Processing conversion for job 7d3c1114-b9f8-42d7-9ba3-a369d347a1f3
ERROR:src.main:Conversion failed for job 7d3c1114-b9f8-42d7-9ba3-a369d347a1f3: [Errno 13] Permission denied: '/app'
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 319, in process_conversion
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
  File "<frozen os>", line 215, in makedirs
  File "<frozen os>", line 225, in makedirs
PermissionError: [Errno 13] Permission denied: '/app'
INFO:     127.0.0.1:60122 - "GET /api/v1/status/7d3c1114-b9f8-42d7-9ba3-a369d347a1f3 HTTP/1.1" 200 OK
INFO:src.main:Started conversion job 71986f35-660f-40f4-87f2-7f34a654f0f7
INFO:     127.0.0.1:45482 - "POST /api/v1/convert HTTP/1.1" 200 OK
INFO:src.main:Processing conversion for job 71986f35-660f-40f4-87f2-7f34a654f0f7
ERROR:src.main:Conversion failed for job 71986f35-660f-40f4-87f2-7f34a654f0f7: [Errno 13] Permission denied: '/app'
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 319, in process_conversion
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
  File "<frozen os>", line 215, in makedirs
  File "<frozen os>", line 225, in makedirs
PermissionError: [Errno 13] Permission denied: '/app'
INFO:     127.0.0.1:41626 - "GET /api/v1/status/71986f35-660f-40f4-87f2-7f34a654f0f7 HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/utils/rate_limiter.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [5746]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [29234]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: ollama/mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 404 Not Found"
ERROR:src.utils.rate_limiter:Ollama LLM test failed: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.utils.rate_limiter:Failed to create Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.crew.conversion_crew:Failed to initialize Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
WARNING:src.main:Attempt 1/10: Ollama LLM initialization failed: Ollama LLM initialization failed: model "ollama/mistral" not found, try pulling it first (status code: 404). Retrying in 5 seconds...
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: ollama/mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 404 Not Found"
ERROR:src.utils.rate_limiter:Ollama LLM test failed: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.utils.rate_limiter:Failed to create Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.crew.conversion_crew:Failed to initialize Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
WARNING:src.main:Attempt 2/10: Ollama LLM initialization failed: Ollama LLM initialization failed: model "ollama/mistral" not found, try pulling it first (status code: 404). Retrying in 5 seconds...
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: ollama/mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 404 Not Found"
ERROR:src.utils.rate_limiter:Ollama LLM test failed: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.utils.rate_limiter:Failed to create Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.crew.conversion_crew:Failed to initialize Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
WARNING:src.main:Attempt 3/10: Ollama LLM initialization failed: Ollama LLM initialization failed: model "ollama/mistral" not found, try pulling it first (status code: 404). Retrying in 5 seconds...
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: ollama/mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 404 Not Found"
ERROR:src.utils.rate_limiter:Ollama LLM test failed: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.utils.rate_limiter:Failed to create Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.crew.conversion_crew:Failed to initialize Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
WARNING:src.main:Attempt 4/10: Ollama LLM initialization failed: Ollama LLM initialization failed: model "ollama/mistral" not found, try pulling it first (status code: 404). Retrying in 5 seconds...
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: ollama/mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 404 Not Found"
ERROR:src.utils.rate_limiter:Ollama LLM test failed: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.utils.rate_limiter:Failed to create Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.crew.conversion_crew:Failed to initialize Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
WARNING:src.main:Attempt 5/10: Ollama LLM initialization failed: Ollama LLM initialization failed: model "ollama/mistral" not found, try pulling it first (status code: 404). Retrying in 5 seconds...
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: ollama/mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 404 Not Found"
ERROR:src.utils.rate_limiter:Ollama LLM test failed: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.utils.rate_limiter:Failed to create Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.crew.conversion_crew:Failed to initialize Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
WARNING:src.main:Attempt 6/10: Ollama LLM initialization failed: Ollama LLM initialization failed: model "ollama/mistral" not found, try pulling it first (status code: 404). Retrying in 5 seconds...
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: ollama/mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 404 Not Found"
ERROR:src.utils.rate_limiter:Ollama LLM test failed: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.utils.rate_limiter:Failed to create Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.crew.conversion_crew:Failed to initialize Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
WARNING:src.main:Attempt 7/10: Ollama LLM initialization failed: Ollama LLM initialization failed: model "ollama/mistral" not found, try pulling it first (status code: 404). Retrying in 5 seconds...
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: ollama/mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 404 Not Found"
ERROR:src.utils.rate_limiter:Ollama LLM test failed: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.utils.rate_limiter:Failed to create Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.crew.conversion_crew:Failed to initialize Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
WARNING:src.main:Attempt 8/10: Ollama LLM initialization failed: Ollama LLM initialization failed: model "ollama/mistral" not found, try pulling it first (status code: 404). Retrying in 5 seconds...
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: ollama/mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 404 Not Found"
ERROR:src.utils.rate_limiter:Ollama LLM test failed: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.utils.rate_limiter:Failed to create Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.crew.conversion_crew:Failed to initialize Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
WARNING:src.main:Attempt 9/10: Ollama LLM initialization failed: Ollama LLM initialization failed: model "ollama/mistral" not found, try pulling it first (status code: 404). Retrying in 5 seconds...
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: ollama/mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 404 Not Found"
ERROR:src.utils.rate_limiter:Ollama LLM test failed: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.utils.rate_limiter:Failed to create Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
ERROR:src.crew.conversion_crew:Failed to initialize Ollama LLM: model "ollama/mistral" not found, try pulling it first (status code: 404)
WARNING:src.main:Attempt 10/10: Ollama LLM initialization failed: Ollama LLM initialization failed: model "ollama/mistral" not found, try pulling it first (status code: 404). Retrying in 5 seconds...
ERROR:src.main:Failed to initialize AI Engine: Failed to initialize Ollama LLM after 10 retries.
Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 202, in startup_event
    raise RuntimeError(f"Failed to initialize Ollama LLM after {max_retries} retries.")
RuntimeError: Failed to initialize Ollama LLM after 10 retries.
ERROR:    Traceback (most recent call last):
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 202, in startup_event
    raise RuntimeError(f"Failed to initialize Ollama LLM after {max_retries} retries.")
RuntimeError: Failed to initialize Ollama LLM after 10 retries.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/starlette/routing.py", line 692, in lifespan
    async with self.lifespan_context(app) as maybe_state:
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/starlette/routing.py", line 569, in __aenter__
    await self._router.startup()
  File "/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/starlette/routing.py", line 669, in startup
    await handler()
  File "/home/<USER>/ModPorter-AI/ai-engine/src/main.py", line 208, in startup_event
    raise HTTPException(status_code=503, detail="Service initialization failed")
fastapi.exceptions.HTTPException: 503: Service initialization failed

ERROR:    Application startup failed. Exiting.
WARNING:  WatchFiles detected changes in 'src/utils/rate_limiter.py'. Reloading...
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [29632]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.utils.rate_limiter:Modified model property for CrewAI compatibility: ollama/mistral
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [29830]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.utils.rate_limiter:Modified model property for CrewAI compatibility: ollama/mistral
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/crew/conversion_crew.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [29830]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [30152]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.utils.rate_limiter:Modified model property for CrewAI compatibility: ollama/mistral
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'tests/conftest.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [30152]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [36739]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.utils.rate_limiter:Modified model property for CrewAI compatibility: ollama/mistral
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'tests/integration/test_mod_conversion_comparison.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [36739]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [36901]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.utils.rate_limiter:Modified model property for CrewAI compatibility: ollama/mistral
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/agents/java_analyzer.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [36901]
/home/<USER>/.pyenv/versions/3.11.8/lib/python3.11/site-packages/pydub/utils.py:170: RuntimeWarning: Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work
  warn("Couldn't find ffmpeg or avconv - defaulting to ffmpeg, but may not work", RuntimeWarning)
WARNING:src.utils.embedding_generator:sentence-transformers not available. EmbeddingGenerator will be disabled.
INFO:     Started server process [46090]
INFO:     Waiting for application startup.
INFO:src.main:Starting ModPorter AI Engine...
INFO:src.main:Redis connection established
INFO:src.main:RedisJobManager initialized
INFO:src.main:SmartAssumptionEngine initialized
INFO:src.crew.conversion_crew:Using Ollama with model: mistral
INFO:src.utils.rate_limiter:Creating Ollama LLM with ChatOllama model: mistral
INFO:httpx:HTTP Request: POST http://localhost:11434/api/chat "HTTP/1.1 200 OK"
INFO:src.utils.rate_limiter:Ollama LLM test successful: <class 'langchain_core.messages.ai.AIMessage'>
INFO:src.utils.rate_limiter:Modified model property for CrewAI compatibility: ollama/mistral
INFO:src.crew.conversion_crew:Successfully initialized Ollama LLM
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
ERROR:src.utils.embedding_generator:sentence-transformers not available. Cannot initialize EmbeddingGenerator.
INFO:src.main:ModPorterConversionCrew initialized
INFO:src.main:ModPorter AI Engine startup complete
INFO:     Application startup complete.
