import json
import logging
import random
import time
from typing import List, Dict, Tuple, Any, Optional

logger = logging.getLogger(__name__)

class TestScenarioGenerator:
    def __init__(self, framework: 'TestFramework'):
        self.framework = framework

    def load_scenarios_from_file(self, scenario_path: str) -> List[Dict[str, Any]]:
        """Uses the framework's method to load scenarios."""
        return self.framework.load_scenarios(scenario_path)

    def generate_dynamic_scenarios(self, base_scenario: Dict[str, Any], variations: int) -> List[Dict[str, Any]]:
        """
        Placeholder for more advanced scenario generation.
        For now, it just replicates the base_scenario 'variations' times with a modified name.
        """
        generated_scenarios = []
        for i in range(variations):
            new_scenario = base_scenario.copy() # Shallow copy is fine for this structure
            new_scenario['name'] = f"{base_scenario.get('name', 'Scenario')} - Variation {i+1}"
            generated_scenarios.append(new_scenario)
        logger.info(f"Generated {len(generated_scenarios)} dynamic scenarios for base: {base_scenario.get('name')}")
        return generated_scenarios

class TestFramework:
    def __init__(self):
        self.test_results_summary: List[Dict[str, Any]] = []

    def load_scenarios(self, scenario_path: str) -> List[Dict[str, Any]]:
        """Loads test scenarios from a JSON file."""
        try:
            with open(scenario_path, 'r') as f:
                data = json.load(f)
            scenarios = data.get('scenarios', [])
            logger.info(f"Successfully loaded {len(scenarios)} scenarios from {scenario_path}")
            return scenarios
        except FileNotFoundError:
            logger.error(f"Scenario file not found: {scenario_path}")
            return []
        except json.JSONDecodeError:
            logger.error(f"Error decoding JSON from {scenario_path}")
            return []

    def execute_scenario(self, scenario: Dict[str, Any]) -> Tuple[bool, str, int]:
        """
        Simulates the execution of a single test scenario.
        Returns a tuple of (success_status, details, execution_time_ms).
        """
        scenario_name = scenario.get('name', 'Unnamed Scenario')
        logger.info(f"Executing scenario: {scenario_name}")

        start_time = time.time()

        # Placeholder for simulated environment interaction
        # E.g., client.place_block(block_type), client.interact_entity(entity_id)
        # print(f"  Simulating steps for: {scenario_name}") # Can be verbose
        for step_idx, step in enumerate(scenario.get('steps', [])):
            # print(f"    - Step {step_idx + 1}: {step}") # Can be verbose
            time.sleep(random.uniform(0.01, 0.05)) # Simulate work for each step

        # Simulate success/failure
        success = random.choice([True, True, True, False]) # Weighted towards success

        details = ""
        if success:
            details = f"Scenario '{scenario_name}' executed successfully."
            # logger.info(details) # Can be verbose
        else:
            details = f"Scenario '{scenario_name}' failed. Expected: '{scenario.get('expected_outcome', 'Not specified')}'"
            logger.warning(details)

        end_time = time.time()
        execution_time_ms = int((end_time - start_time) * 1000)

        return success, details, execution_time_ms

    def collect_results(self, scenario: Dict[str, Any], success: bool, details_from_execution: str, execution_time_ms: int, error_message_arg: Optional[str] = None, performance_metrics: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Collects and formats the result of a single test case.
        Returns a dictionary representing the test case result.
        """

        final_error_message = None
        if not success:
            if error_message_arg:
                final_error_message = error_message_arg
            else:
                # If no specific error message is passed, use the details from execution as the error.
                final_error_message = details_from_execution

        result = {
            "test_name": scenario.get('name', 'Unnamed Test'),
            "test_category": scenario.get('category', 'general'),
            "status": "passed" if success else "failed",
            "execution_time_ms": execution_time_ms,
            "error_message": final_error_message,
            "performance_metrics": performance_metrics if performance_metrics else {},
            "details": details_from_execution # This field always contains the raw execution details
        }
        self.test_results_summary.append(result)
        return result

    def run_test_suite(self, scenarios: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Runs a suite of test scenarios and collects their results.
        """
        suite_results = []
        if not scenarios:
            logger.warning("No scenarios provided to run_test_suite.")
            return suite_results

        logger.info(f"Starting test suite with {len(scenarios)} scenarios.")
        for scenario in scenarios:
            success, details_from_exec, execution_time_ms = self.execute_scenario(scenario)

            # This is where a more specific error message could be generated if needed,
            # based on 'details_from_exec' or other logic.
            # For now, we'll pass 'details_from_exec' as a candidate for the error message
            # if no other specific error message is formed.
            error_msg_for_collection = details_from_exec if not success else None

            perf_metrics = None
            if scenario.get('category') == 'performance':
                perf_metrics = {"cpu_usage_percent": round(random.uniform(5, 60), 2), "memory_usage_mb": random.randint(50, 500)}

            test_case_result = self.collect_results(
                scenario,
                success,
                details_from_exec,
                execution_time_ms,
                error_message_arg=error_msg_for_collection, # Pass the potentially more specific error
                performance_metrics=perf_metrics
            )
            suite_results.append(test_case_result)

        logger.info(f"Test suite finished. {len(suite_results)} results collected.")
        return suite_results

if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - [%(name)s] - %(message)s')

    framework = TestFramework()
    scenario_gen = TestScenarioGenerator(framework) # Added this line

    dummy_scenarios_path = "dummy_scenarios.json"
    # The actual example_scenarios.json is in a subdirectory,
    # the dummy file is fine for the __main__ block's original purpose.
    dummy_data = {
        "scenarios": [
            {
                "name": "Basic Block Placement",
                "category": "functional",
                "description": "Test placing and breaking custom blocks",
                "steps": ["Place block", "Interact with block", "Break block"],
                "expected_outcome": "Block behaves as intended"
            },
            {
                "name": "Performance Under Load",
                "category": "performance",
                "description": "Test add-on performance with many instances",
                "steps": ["Spawn 1000 entities", "Monitor performance", "Clean up"],
                "expected_outcome": "Performance remains acceptable (FPS > 30)"
            },
            {
                "name": "Complex Crafting Recipe",
                "category": "functional",
                "description": "Test a complex crafting recipe with multiple ingredients.",
                "steps": ["Acquire ingredient A", "Acquire ingredient B", "Open crafting table", "Place ingredients in pattern", "Retrieve crafted item"],
                "expected_outcome": "Correct item is crafted and received."
            },
            {
                "name": "Another Performance Test",
                "category": "performance",
                "description": "Test rendering many particles.",
                "steps": ["Trigger particle effect", "Monitor frame rate"],
                "expected_outcome": "Frame rate stays above critical threshold."
            }
        ]
    }
    with open(dummy_scenarios_path, 'w') as f:
        json.dump(dummy_data, f, indent=2)
    logger.info(f"Created dummy scenario file: {dummy_scenarios_path}")

    # Demonstrate loading via generator
    loaded_via_generator = scenario_gen.load_scenarios_from_file(dummy_scenarios_path)
    print(f"\n--- Scenarios loaded via TestScenarioGenerator ({len(loaded_via_generator)}) ---")
    if loaded_via_generator:
        print(f"First scenario loaded by generator: {loaded_via_generator[0]['name']}")

    # Example of dynamic generation (simple)
    if loaded_via_generator:
        base_functional_scenario = next((s for s in loaded_via_generator if s['category'] == 'functional'), None)
        if base_functional_scenario:
            dynamic_functional = scenario_gen.generate_dynamic_scenarios(base_functional_scenario, 2)
            print(f"\n--- Dynamically generated scenarios ({len(dynamic_functional)}) ---")
            for ds in dynamic_functional:
                print(f"  - {ds['name']}")

    # Original loaded_scenarios for run_test_suite
    loaded_scenarios = framework.load_scenarios(dummy_scenarios_path) # This line was already here, kept for original flow

    if loaded_scenarios:
        print(f"\n--- Running Test Suite ({len(loaded_scenarios)} scenarios from original load) ---")
        results = framework.run_test_suite(loaded_scenarios)
        print("\n--- Test Suite Results (Individual) ---")
        for i, res in enumerate(results):
            print(f"Result {i+1}/{len(results)} ({res.get('test_name')} - {res.get('status')}):")
            print(f"  Category: {res.get('test_category')}")
            print(f"  Time: {res.get('execution_time_ms')}ms")
            if res.get('status') == 'failed':
                print(f"  Error: {res.get('error_message')}")
            if res.get('performance_metrics'):
                print(f"  Perf Metrics: {res.get('performance_metrics')}")
            print("-" * 20)

        print(f"\n--- Summary of All Test Results ({len(framework.test_results_summary)} collected) ---")

    import os
    try:
        os.remove(dummy_scenarios_path)
        logger.info(f"Successfully cleaned up {dummy_scenarios_path}")
    except OSError as e:
        logger.error(f"Error removing {dummy_scenarios_path}: {e}")
