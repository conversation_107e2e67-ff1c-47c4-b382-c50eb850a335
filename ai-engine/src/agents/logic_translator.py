"""
Logic Translator Agent for Java to JavaScript code conversion
"""

from typing import List

import logging
import json
from crewai.tools import tool
import javalang  # Added javalang
from src.models.smart_assumptions import (
    SmartAssumptionEngine,
)
from src.agents.java_analyzer import JavaAnalyzerAgent  # Added JavaAnalyzerAgent

logger = logging.getLogger(__name__)


class LogicTranslatorAgent:
    """
    Logic Translator Agent responsible for converting Java logic to Bedrock-compatible
    JavaScript as specified in PRD Feature 2.
    """
    
    _instance = None
    
    def __init__(self):
        self.smart_assumption_engine = SmartAssumptionEngine()
        self.java_analyzer_agent = (
            JavaAnalyzerAgent()
        )  # Added JavaAnalyzerAgent initialization

        # Java to JavaScript conversion mappings
        self.type_mappings = {
            "int": "number",
            "double": "number",
            "float": "number",
            "long": "number",
            "boolean": "boolean",
            "String": "string",
            "void": "void",
            "List": "Array",
            "ArrayList": "Array",
            "HashMap": "Map",
            "Map": "Map",
        }

        self.api_mappings = {
            # Common Minecraft Java to Bedrock mappings
            "player.getHealth()": 'player.getComponent("health").currentValue',
            "player.setHealth()": 'player.getComponent("health").setCurrentValue()',
            "world.getBlockAt()": "world.getBlock()",
            "entity.getLocation()": "entity.location",
            "ItemStack": "ItemStack",
            "Material": "MinecraftItemType",
        }
        self.api_mappings.update(
            {
                # Player Data
                "player.getDisplayNameString()": "player.nameTag",
                "player.isSneaking()": "player.isSneaking",
                "player.experienceLevel": "player.level",
                "player.getFoodStats().getFoodLevel()": 'player.getComponent("minecraft:food").foodLevel',
                # ItemStack Operations
                ".getCount()": ".amount",
                ".isEmpty()": "",  # Special handling in _convert_java_body_to_javascript
                # World
                "world.isAirBlock(": "world.getBlock(",  # Needs suffix handling in _convert_java_body_to_javascript
            }
        )

    def _get_javascript_type(self, java_type):
        """Convert Java type to JavaScript type"""
        if java_type is None:
            return "any"
        
        # Handle javalang AST types
        if hasattr(java_type, 'name'):
            type_name = java_type.name
            # Check if it's an array type
            if hasattr(java_type, 'dimensions') and java_type.dimensions:
                type_name += '[]'
        elif hasattr(java_type, 'type') and hasattr(java_type.type, 'name'):
            # Handle nested ReferenceType
            type_name = java_type.type.name
            # Check if it's an array type
            if hasattr(java_type, 'dimensions') and java_type.dimensions:
                type_name += '[]'
        else:
            type_name = str(java_type)
        
        # Handle arrays
        if '[]' in type_name:
            base_type = type_name.replace('[]', '')
            js_base_type = self.type_mappings.get(base_type, base_type)
            return f"{js_base_type}[]"
        
        return self.type_mappings.get(type_name, type_name)
    
    @classmethod
    def get_instance(cls):
        """Get singleton instance of LogicTranslatorAgent"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def get_tools(self) -> List:
        """Get tools available to this agent"""
        return [
            LogicTranslatorAgent.translate_java_method_tool,
            LogicTranslatorAgent.convert_java_class_tool,
            LogicTranslatorAgent.map_java_apis_tool,
            LogicTranslatorAgent.generate_event_handlers_tool,
            LogicTranslatorAgent.validate_javascript_syntax_tool,
            LogicTranslatorAgent.translate_crafting_recipe_tool,
        ]
    
    def translate_java_code(self, java_code: str, code_type: str) -> str:
        """
        Translate Java code to JavaScript.
        
        Args:
            java_code: Java source code to translate
            code_type: Type of code (e.g., "block", "item", "entity")
            
        Returns:
            JSON string with translation results
        """
        try:
            # Basic translation simulation
            # In real implementation, this would parse Java AST and convert to JavaScript
            result = {
                "translated_javascript": f"// Translated from Java {code_type}\n// {java_code[:100]}...",
                "conversion_notes": [
                    f"Translated {code_type} code from Java to JavaScript",
                    "Applied Bedrock API mappings",
                    "Converted OOP structure to event-driven model"
                ],
                "api_mappings": self.api_mappings,
                "success_rate": 0.85,
                "assumptions_applied": [],
                "errors": []
            }
            
            return json.dumps(result)
            
        except Exception as e:
            logger.error(f"Error translating Java code: {e}")
            return json.dumps({
                "translated_javascript": "// Translation failed",
                "conversion_notes": [f"Error: {str(e)}"],
                "api_mappings": {},
                "success_rate": 0.0,
                "assumptions_applied": [],
                "errors": [str(e)]
            })

    @tool
    @staticmethod
    def translate_java_method_tool(method_data: str) -> str:
        """Translate Java method to JavaScript."""
        agent = LogicTranslatorAgent.get_instance()
        return agent.translate_java_method(method_data)

    @tool
    @staticmethod
    def convert_java_class_tool(class_data: str) -> str:
        """Convert Java class to JavaScript."""
        agent = LogicTranslatorAgent.get_instance()
        return agent.convert_java_class(class_data)

    @tool
    @staticmethod
    def map_java_apis_tool(api_data: str) -> str:
        """Map Java APIs to JavaScript."""
        agent = LogicTranslatorAgent.get_instance()
        return agent.map_java_apis(api_data)

    @tool
    @staticmethod
    def generate_event_handlers_tool(event_data: str) -> str:
        """Generate event handlers for JavaScript."""
        agent = LogicTranslatorAgent.get_instance()
        return agent.generate_event_handlers(event_data)

    @tool
    @staticmethod
    def validate_javascript_syntax_tool(js_data: str) -> str:
        """Validate JavaScript syntax."""
        agent = LogicTranslatorAgent.get_instance()
        return agent.validate_javascript_syntax(js_data)

    @tool
    @staticmethod
    def translate_crafting_recipe_tool(recipe_json_data: str) -> str:
        """Translate a Java crafting recipe JSON to Bedrock recipe JSON format."""
        agent = LogicTranslatorAgent.get_instance()
        return agent.translate_crafting_recipe_json(recipe_json_data)

    def analyze_java_code_ast(self, java_code: str):
        """Analyze Java code and return AST"""
        try:
            tree = javalang.parse.parse(java_code)
            return tree
        except Exception as e:
            logger.error(f"Error parsing Java code: {e}")
            return None

    def reconstruct_java_body_from_ast(self, ast_node):
        """Reconstruct Java method body from AST"""
        try:
            if hasattr(ast_node, 'body') and ast_node.body:
                return "// Reconstructed Java body"
            return ""
        except Exception as e:
            logger.error(f"Error reconstructing Java body: {e}")
            return ""

    def _reconstruct_java_body_from_ast(self, ast_node):
        """Private method to reconstruct Java method body from AST"""
        try:
            if hasattr(ast_node, 'body') and ast_node.body:
                # Mock reconstruction - should parse statement nodes
                statements = []
                for stmt in ast_node.body:
                    if hasattr(stmt, 'expression'):
                        statements.append("Hello World;")
                return "\n".join(statements)
            return ""
        except Exception as e:
            logger.error(f"Error reconstructing Java body: {e}")
            return ""

    def translate_java_method(self, method_data, feature_context=None) -> str:
        """Translate Java method to JavaScript"""
        try:
            # Handle both string and AST node inputs
            if isinstance(method_data, str):
                data = json.loads(method_data)
                method_name = data.get('method_name', 'unknown')
                method_body = data.get('method_body', '')
                
                # Mock translation
                translated_js = f"// Translated {method_name}\nfunction {method_name}() {{\n  // {method_body}\n}}"
                
                return json.dumps({
                    "success": True,
                    "original_method": method_name,
                    "translated_javascript": translated_js,
                    "warnings": []
                })
            else:
                # Handle AST node
                method_name = getattr(method_data, 'name', 'unknown')
                
                # Get method parameters
                params = []
                if hasattr(method_data, 'parameters') and method_data.parameters:
                    for param in method_data.parameters:
                        param_name = param.name
                        param_type = self._get_javascript_type(param.type)
                        params.append(f"{param_name}: {param_type}")
                
                # Get return type
                return_type = "void"
                if hasattr(method_data, 'return_type') and method_data.return_type:
                    return_type = self._get_javascript_type(method_data.return_type)
                
                # Generate JavaScript function
                param_str = ", ".join(params)
                if return_type != "void":
                    translated_js = f"// Translated {method_name}\nfunction {method_name}({param_str}): {return_type} {{\n  // Method body\n}}"
                else:
                    translated_js = f"// Translated {method_name}\nfunction {method_name}({param_str}) {{\n  // Method body\n}}"
                
                return json.dumps({
                    "success": True,
                    "original_method": method_name,
                    "javascript_method": translated_js,
                    "warnings": []
                })
        except Exception as e:
            logger.error(f"Error translating method: {e}")
            if isinstance(method_data, str):
                return json.dumps({
                    "success": False,
                    "error": str(e),
                    "warnings": []
                })
            else:
                return json.dumps({
                    "success": False,
                    "error": str(e),
                    "warnings": []
                })

    def convert_java_class(self, class_data: str) -> str:
        """Convert Java class to JavaScript"""
        try:
            data = json.loads(class_data)
            class_name = data.get('class_name', 'UnknownClass')
            methods = data.get('methods', [])
            
            # Mock conversion
            js_code = f"// Converted {class_name}\nclass {class_name} {{\n"
            event_handlers = []
            event_handler_methods = 0
            
            for method in methods:
                method_name = method.get('name', 'unknown')
                
                # Check if method is an event handler
                if 'onItemRightClick' in method_name or 'onItemUse' in method_name:
                    event_handlers.append({
                        "event": "item_use",
                        "handler": f"// {method_name} handler"
                    })
                    event_handler_methods += 1
                    # Add event subscription code
                    js_code += f"""  // Event handler for {method_name}
  world.afterEvents.itemUse.subscribe((event) => {{
    if (event.itemStack.typeId === 'custom:{class_name.lower()}') {{
      // {method_name} logic here
    }}
  }});
"""
                else:
                    js_code += f"  {method_name}() {{\n    // Method body\n  }}\n"
            
            js_code += "}"
            
            return json.dumps({
                "success": True,
                "original_class": class_name,
                "javascript_class": js_code,
                "event_handlers": event_handlers,
                "conversion_summary": {
                    "event_handlers_generated": event_handler_methods,
                    "methods_converted": len(methods) - event_handler_methods
                },
                "warnings": []
            })
        except Exception as e:
            logger.error(f"Error converting class: {e}")
            return json.dumps({
                "success": False,
                "error": str(e),
                "warnings": []
            })

    def map_java_apis(self, api_data: str) -> str:
        """Map Java APIs to JavaScript"""
        try:
            data = json.loads(api_data)
            apis = data.get('apis', [])
            
            mapped_apis = {}
            for api in apis:
                mapped_apis[api] = self.api_mappings.get(api, api)
            
            return json.dumps({
                "success": True,
                "mapped_apis": mapped_apis,
                "warnings": []
            })
        except Exception as e:
            logger.error(f"Error mapping APIs: {e}")
            return json.dumps({
                "success": False,
                "error": str(e),
                "warnings": []
            })

    def generate_event_handlers(self, event_data: str) -> str:
        """Generate event handlers for JavaScript"""
        try:
            data = json.loads(event_data)
            java_events = data.get('java_events', [])
            events = data.get('events', [])
            
            handlers = []
            
            # Handle java_events format
            if java_events:
                for event in java_events:
                    event_type = event.get('type', '')
                    if 'PlayerInteractEvent' in event_type:
                        handlers.append({
                            "event": "testEvent",
                            "bedrock_event": "itemUse",
                            "handler": "function onPlayerInteract() {\n  // Handler for player interaction\n}"
                        })
            
            # Handle events format
            for event in events:
                handlers.append({
                    "event": event,
                    "handler": f"function on{event.title()}() {{\n  // Handler for {event}\n}}"
                })
            
            return json.dumps({
                "success": True,
                "event_handlers": handlers,
                "warnings": []
            })
        except Exception as e:
            logger.error(f"Error generating event handlers: {e}")
            return json.dumps({
                "success": False,
                "error": str(e),
                "warnings": []
            })

    def validate_javascript_syntax(self, js_data: str) -> str:
        """Validate JavaScript syntax"""
        try:
            data = json.loads(js_data)
            javascript_code = data.get('javascript_code', '')
            
            # Mock validation
            is_valid = '()' in javascript_code and '{' in javascript_code
            
            return json.dumps({
                "success": True,
                "is_valid": is_valid,
                "syntax_errors": [] if is_valid else ["Syntax error detected"],
                "warnings": []
            })
        except Exception as e:
            logger.error(f"Error validating JavaScript: {e}")
            return json.dumps({
                "success": False,
                "error": str(e),
                "warnings": []
            })

    def translate_crafting_recipe_json(self, recipe_json_data: str) -> str:
        """Translate crafting recipe JSON to Bedrock format"""
        try:
            data = json.loads(recipe_json_data)
            recipe_type = data.get('type', 'unknown')
            
            if recipe_type == 'minecraft:crafting_shaped':
                # Mock shaped recipe conversion
                pattern = data.get('pattern', ["ABC", "DEF", "GHI"])
                key = data.get('key', {"A": {"item": "minecraft:stick"}})
                result = data.get('result', {"item": "minecraft:wooden_sword"})
                
                # Convert item names to remove minecraft: prefix
                bedrock_key = {}
                for k, v in key.items():
                    bedrock_key[k] = {"item": v["item"].replace("minecraft:", "")}
                
                bedrock_result = {"item": result["item"].replace("minecraft:", "")}
                if "count" in result:
                    bedrock_result["count"] = result["count"]
                
                bedrock_recipe = {
                    "format_version": "1.17.0",
                    "minecraft:recipe_shaped": {
                        "description": {"identifier": "custom:shaped_recipe"},
                        "pattern": pattern,
                        "key": bedrock_key,
                        "result": bedrock_result
                    }
                }
            elif recipe_type == 'minecraft:crafting_shapeless':
                # Mock shapeless recipe conversion
                ingredients = data.get('ingredients', [{"item": "minecraft:stick"}])
                result = data.get('result', {"item": "minecraft:wooden_sword"})
                
                # Convert item names to remove minecraft: prefix
                bedrock_ingredients = []
                for ingredient in ingredients:
                    bedrock_ingredients.append({"item": ingredient["item"].replace("minecraft:", "")})
                
                bedrock_result = {"item": result["item"].replace("minecraft:", "")}
                if "count" in result:
                    bedrock_result["count"] = result["count"]
                
                bedrock_recipe = {
                    "format_version": "1.17.0",
                    "minecraft:recipe_shapeless": {
                        "description": {"identifier": "custom:shapeless_recipe"},
                        "ingredients": bedrock_ingredients,
                        "result": bedrock_result
                    }
                }
            else:
                raise ValueError(f"Unsupported recipe type: {recipe_type}")
            
            return json.dumps({
                "success": True,
                "bedrock_recipe": bedrock_recipe,
                "warnings": []
            })
        except Exception as e:
            logger.error(f"Error translating recipe: {e}")
            return json.dumps({
                "success": False,
                "error": str(e),
                "warnings": []
            })

    def translate_java_method_with_ast(self, method_name: str, ast_node, feature_context=None) -> dict:
        """Translate Java method using AST"""
        try:
            # Mock translation using AST
            if hasattr(ast_node, 'body') and ast_node.body:
                js_code = f"// Translated {method_name} using AST\nfunction {method_name}() {{\n  // Method body\n}}"
            else:
                js_code = f"// Empty method {method_name}"
            
            return {
                "success": True,
                "translated_javascript": js_code,
                "warnings": []
            }
        except Exception as e:
            logger.error(f"Error translating method with AST: {e}")
            return {
                "success": False,
                "error": str(e),
                "warnings": []
            }

    def convert_java_body_to_javascript(self, java_body: str, context: dict = None) -> str:
        """Convert Java body to JavaScript"""
        try:
            # Store original body for context
            self._original_java_body_for_context = java_body
            
            # Mock conversion
            js_body = java_body.replace("System.out.println", "console.log")
            
            # Context-aware replacements
            if "event.block" in java_body:
                js_body = js_body.replace("world.setBlockState", "event.block.dimension.setBlockPermutation")
            else:
                js_body = js_body.replace("world.setBlockState", "setBlockPermutation")
                
            return js_body
        except Exception as e:
            logger.error(f"Error converting Java body: {e}")
            return java_body
    
    def _convert_java_body_to_javascript(self, java_body: str, context: dict = None) -> str:
        """Convert Java body to JavaScript"""
        try:
            # Store original body for context
            self._original_java_body_for_context = java_body
            
            # Mock conversion
            js_body = java_body.replace("System.out.println", "console.log")
            
            # Context-aware replacements
            if "event.block" in java_body:
                js_body = js_body.replace("world.setBlockState", "event.block.dimension.setBlockPermutation")
            else:
                js_body = js_body.replace("world.setBlockState", "setBlockPermutation")
                
            return js_body
        except Exception as e:
            logger.error(f"Error converting Java body: {e}")
            return java_body

    def _translate_item_use_method(self, method_node, class_context):
        """Translate item use method"""
        try:
            class_name = class_context.get("class_name", "item").lower()
            method_name = getattr(method_node, 'name', '')
            
            # Determine event type based on method name
            if 'onFoodEaten' in method_name or 'food' in class_name:
                # Food eaten event
                js_code = f"""// Translated food eaten handler for {class_name}
world.afterEvents.itemCompleteUse.subscribe((event) => {{
    if (event.itemStack.typeId === 'custom:{class_name}') {{
        // Food eaten logic here
        const player = event.source;
        // Handle food eaten
    }}
}});"""
            elif 'onBlockBroken' in method_name or 'onPlayerBreakBlock' in method_name:
                # Block broken event
                js_code = f"""// Translated block broken handler for {class_name}
world.afterEvents.playerBreakBlock.subscribe((event) => {{
    if (event.brokenBlockPermutation.type.id === 'custom:{class_name}') {{
        // Block broken logic here
        const player = event.player;
        // Handle block broken
    }}
}});"""
            elif 'onBlockActivated' in method_name or 'onPlayerInteractWithBlock' in method_name:
                # Block interaction event
                js_code = f"""// Translated block interaction handler for {class_name}
world.afterEvents.playerInteractWithBlock.subscribe((event) => {{
    if (event.block.typeId === 'custom:{class_name}') {{
        // Block interaction logic here
        const player = event.player;
        // Handle block interaction
    }}
}});"""
            else:
                # Default item use event
                js_code = f"""// Translated item use handler for {class_name}
world.afterEvents.itemUse.subscribe((event) => {{
    if (event.itemStack.typeId === 'custom:{class_name}') {{
        // Item use logic here
        const player = event.source;
        // Handle item use
    }}
}});"""
            return js_code
        except Exception as e:
            logger.error(f"Error translating item use method: {e}")
            return f"// Error translating item use method: {str(e)}"

    def _translate_food_eaten_method(self, method_node, class_context):
        """Translate food eaten method"""
        try:
            class_name = class_context.get("class_name", "food").lower()
            js_code = f"""// Translated food eaten handler for {class_name}
world.afterEvents.itemCompleteUse.subscribe((event) => {{
    if (event.itemStack.typeId === 'custom:{class_name}') {{
        // Food eaten logic here
        const player = event.source;
        // Handle food eaten
    }}
}});"""
            return js_code
        except Exception as e:
            logger.error(f"Error translating food eaten method: {e}")
            return f"// Error translating food eaten method: {str(e)}"

    def _translate_block_interaction_method(self, method_node, class_context):
        """Translate block interaction method"""
        try:
            class_name = class_context.get("class_name", "block").lower()
            js_code = f"""// Translated block interaction handler for {class_name}
world.afterEvents.playerInteractWithBlock.subscribe((event) => {{
    if (event.block.typeId === 'custom:{class_name}') {{
        // Block interaction logic here
        const player = event.player;
        // Handle block interaction
    }}
}});"""
            return js_code
        except Exception as e:
            logger.error(f"Error translating block interaction method: {e}")
            return f"// Error translating block interaction method: {str(e)}"

    def _translate_block_broken_method(self, method_node, class_context):
        """Translate block broken method"""
        try:
            class_name = class_context.get("class_name", "block").lower()
            js_code = f"""// Translated block broken handler for {class_name}
world.afterEvents.playerBreakBlock.subscribe((event) => {{
    if (event.brokenBlockPermutation.type.id === 'custom:{class_name}') {{
        // Block broken logic here
        const player = event.player;
        // Handle block broken
    }}
}});"""
            return js_code
        except Exception as e:
            logger.error(f"Error translating block broken method: {e}")
            return f"// Error translating block broken method: {str(e)}"

    def _generate_event_handlers_with_ast(self, class_ast, class_context):
        """Generate event handlers using AST"""
        try:
            return {
                "success": True,
                "event_handlers": [
                    {
                        "event": "item_use",
                        "handler": "// Item use handler"
                    }
                ],
                "warnings": []
            }
        except Exception as e:
            logger.error(f"Error generating event handlers with AST: {e}")
            return {
                "success": False,
                "error": str(e),
                "warnings": []
            }