[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
asyncio_default_test_loop_scope = function
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --pythonpath=src
    --cov=src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=40
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    asyncio: Async tests
    ai: Tests that use AI services (may be slow/expensive)
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::FutureWarning
    ignore::pydantic.warnings.PydanticDeprecatedSince20
    ignore::pytest.PytestUnknownMarkWarning