# AI Engine Environment Variables
# Copy this file to .env and fill in your actual values

# Server Configuration
HOST=0.0.0.0
PORT=8001
DEBUG=true

# AI API Keys
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
GOOGLE_API_KEY=your-google-api-key

# Model Configuration
DEFAULT_MODEL=gpt-4
FALLBACK_MODEL=gpt-3.5-turbo
MAX_TOKENS=4000
TEMPERATURE=0.7

# CrewAI Configuration
CREW_SIZE=3
MAX_ITERATIONS=5
ENABLE_MEMORY=true

# LangChain Configuration
LANGCHAIN_TRACING_V2=false
LANGCHAIN_ENDPOINT=
LANGCHAIN_API_KEY=
LANGCHAIN_PROJECT=modporter-ai

# Vector Database (if using)
VECTORDB_URL=
VECTORDB_API_KEY=
EMBEDDING_MODEL=text-embedding-ada-002

# Processing Configuration
MAX_CONCURRENT_JOBS=5
JOB_TIMEOUT=1800
QUEUE_NAME=conversion_queue

# File Processing
TEMP_DIR=./temp
PROCESSING_DIR=./processing
OUTPUT_DIR=./output

# Redis Configuration (for job queue)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=1

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9091

# Rate Limiting
REQUESTS_PER_MINUTE=60
BURST_LIMIT=10

# Testing
MOCK_AI_RESPONSES=false
TEST_DATA_DIR=./test_data

# Ollama Configuration
USE_OLLAMA=false
OLLAMA_MODEL=llama3.2
OLLAMA_BASE_URL=http://localhost:11434