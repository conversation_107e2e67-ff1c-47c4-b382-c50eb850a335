{"name": "modporter-ai-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "storybook:test": "test-storybook", "storybook:build": "storybook build --output-dir dist-storybook"}, "dependencies": {"axios": "^1.10.0", "mermaid": "^11.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-router-dom": "^7.6.3"}, "devDependencies": {"@eslint/js": "^9.30.1", "@storybook/addon-docs": "^9.0.15", "@storybook/addon-onboarding": "^9.0.15", "@storybook/react-vite": "^9.0.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-storybook": "^9.0.15", "globals": "^13.24.0", "jsdom": "^26.1.0", "msw": "^2.10.3", "prettier": "^3.6.2", "storybook": "^9.0.15", "typescript": "^5.8.3", "vite": "^7.0.2", "vitest": "^3.2.4"}}