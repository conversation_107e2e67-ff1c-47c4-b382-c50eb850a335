.conversion-progress-container {
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 8px;
    margin-bottom: 20px;
    font-family: Arial, sans-serif;
}

.progress-bar-container {
    width: 100%;
    background-color: #f0f0f0;
    border-radius: 4px;
    margin: 10px 0;
    height: 20px;
    overflow: hidden; /* Ensures inner bar stays within rounded corners */
}

.progress-bar-filler {
    height: 100%;
    background-color: #4caf50;
    text-align: center;
    line-height: 20px; /* Vertically center text if any */
    color: white;
    border-radius: 4px 0 0 4px; /* Keep left corners rounded */
    transition: width 0.3s ease-in-out;
}

.progress-bar-filler.completed {
    border-radius: 4px; /* All corners rounded when completed */
}

.progress-info p {
    margin: 5px 0;
    color: #333;
}

.progress-info strong {
    color: #111;
}

.download-button {
    background-color: #007bff;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    margin-top: 10px;
    transition: background-color 0.2s ease-in-out;
}

.download-button:hover {
    background-color: #0056b3;
}

.download-button:disabled {
    background-color: #aaa;
    cursor: not-allowed;
}

.error-message {
    color: #D8000C; /* Standard error red */
    background-color: #FFD2D2; /* Light red background */
    border: 1px solid #D8000C;
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
}
